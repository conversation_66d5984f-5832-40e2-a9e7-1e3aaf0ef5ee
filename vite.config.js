import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react({
    include: "**/*.{jsx,tsx,js,ts}",
  })],
  esbuild: {
    loader: "jsx",
    include: /src\/.*\.[jt]sx?$/,
    exclude: [],
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'build',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          router: ['react-router-dom'],
        },
      },
    },
  },
  define: {
    // For compatibility with some libraries that expect process.env
    'process.env': {},
  },
  envPrefix: 'VITE_',
  // Handle public directory
  publicDir: 'public',
  // CSS configuration
  css: {
    postcss: './postcss.config.js',
  },
  // Resolve configuration
  resolve: {
    alias: {
      // Add any path aliases if needed
    },
  },
  // Optimizations
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'antd',
      'react-router-dom',
      'axios',
      'moment',
      'socket.io-client',
    ],
  },
})
