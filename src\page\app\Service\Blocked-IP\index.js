/* eslint-disable object-curly-newline */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable no-unused-vars */
/* eslint-disable import/order */
import React, { useEffect, useRef, useState } from 'react';
import {
    Card, Row, Col,
    Typography,
    Switch,
} from 'antd';
import { useParams } from 'react-router-dom';
import CRUDComponent from '../../../../component/common/CRUD-Component';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import useHttp from '../../../../hooks/use-http';
import ChartSection from '../Block-Api/ChartSection';

const { Text } = Typography;
// let totalEndpointCounts = 0;

const BlockedIPPage = () => {
    const [refresh, setRefresh] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const excelRef = useRef();

    const { environmentID } = useParams();
    const api = useHttp();

    const headers = [
        {
            title: "No.",
            dataIndex: "no",
            key: "no",
            width: 100,
        },
        {
            title: "Endpoint",
            dataIndex: "endPointD",
            key: "endPointD",
            width: 150,
        },
        {
            title: "Type",
            dataIndex: "type",
            key: "type",
            width: 120,
            filters: [
                {
                    text: "Internal",
                    value: "Internal",
                },
                {
                    text: "ThirdParty",
                    value: "ThirdParty",
                },
            ],
            filterMode: "tree",
            filterSearch: true,
        },
        {
            title: "Method",
            dataIndex: "method",
            className: "!text-center",
            key: "method",
            width: 90,
            filters: [
                {
                    text: "GET",
                    value: "GET",
                },
                {
                    text: "POST",
                    value: "POST",
                },
                {
                    text: "PATCH",
                    value: "PATCH",
                },
                {
                    text: "PUT",
                    value: "PUT",
                },
                {
                    text: "DELETE",
                    value: "DELETE",
                },
                {
                    text: "HEAD",
                    value: "HEAD",
                },
                {
                    text: "OPTIONS",
                    value: "OPTIONS",
                },
            ],
            filterMode: "tree",
            filterSearch: true,
        },
        {
            title: "Status",
            dataIndex: "status",
            className: "!text-center",
            key: "status",
            width: 80,
            sorter: true,
        },
        {
            title: "Fail Attempts",
            dataIndex: "failAttempts",
            className: "!text-center",
            key: "failAttempts",
            width: 80,
            sorter: true,
        },
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (value) => {
            // console.log(value);
            setSelectedRowKeys(value);
        },

    };
    const updataeVisibility = (payload) => {
        // console.log(CONSTANTS.API.overview.updateApiVisibility);
        const VISIBILITY_API = { ...CONSTANTS.API.overview.updateApiVisibility };
        // console.log(VISIBILITY_API);
        api.sendRequest(
            VISIBILITY_API,
            () => {
                setRefresh((prev) => !prev);
            },
            payload,
            "Endpoint Unblocked Successfully !!!",
        );
    };

    return (
        <Row gutter={[16, 24]}>
            <Col span={24}>
                <ChartSection />
            </Col>
            <Col span={24}>
                <Card>
                    <div className="flex items-center justify-between gap-3">
                        <Text className="hidden  md:block">Blocked Endpoint</Text>
                    </div>
                    <CRUDComponent
                        // reload={refresh}
                        GET={{
                            API: CONSTANTS.API.blockIP.get,
                            extraQuery: {
                                serviceEnvironmentId: environmentID,
                            },
                            DataModifier: (res, API, Setrefresh) => {
                                return res?.map((ele, i) => {
                                    return {
                                        ...ele,
                                        key: ele,
                                        no:
                                            ele?.id ?? i + 1,
                                        method: ele?.method || "-",
                                        // windowMs: (ele?.windowMs / (60 * 1000))?.toFixed(0) || "-",
                                        // isBlockAfterFaultTag: ele?.isBlockAfterFault ? "Yes" : "No",
                                        // shouldBlockIfNoKeyFoundTag: ele?.shouldBlockIfNoKeyFound ? "Yes" : "No",
                                        endpointTag:
                                            <p className="overflow-hidden break-all">{ele?.endpoint}</p>
                                            || "-",
                                        ip: ele?.ip || "-",
                                        // deleteWithReason: <Button onClick={() => setDeleteOpen(el?.id)} type="primary" className="textcolor">
                                        //     <DeleteOutlined />
                                        // </Button>,
                                    };
                                });
                            },
                            column: CONSTANTS.TABLE.BLOCKED_IP,
                        }}
                        isSearch
                    />
                </Card>
            </Col>
        </Row>
    );
};

export default BlockedIPPage;
